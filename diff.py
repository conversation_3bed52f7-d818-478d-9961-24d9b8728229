import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill

# 文件路径
file_1171 = "spelldbc1171.xlsx"  # 基准文件
file_1180 = "spelldbc1180.xlsx"  # 比较文件
output_file = "diff_resultspell.xlsx"
sql_output_file = "update_commands.sql"  # SQL输出文件

# 忽略这些字段（但保留Name_deDE和Description_deDE用于固定显示）
ignore_columns = {
    "SpellPriority","Name_enUS","Name_enGB","Name_koKR","Name_frFR","Name_enCN","Name_zhCN","Name_enTW","Name_Mask",
    "NameSubtext_enUS","NameSubtext_enGB","NameSubtext_koKR","NameSubtext_frFR","NameSubtext_deDE","NameSubtext_enCN","NameSubtext_zhCN","NameSubtext_enTW","NameSubtext_Mask",
    "Description_enUS","Description_enGB","Description_koKR","Description_frFR","Description_enCN","Description_zhCN","Description_enTW","Description_Mask",
    "AuraDescription_enUS","AuraDescription_enGB","AuraDescription_koKR","AuraDescription_frFR","AuraDescription_deDE","AuraDescription_enCN","AuraDescription_zhCN","AuraDescription_enTW","AuraDescription_Mask"
}

# Excel表中和数据库spell_template的字段映射关系
field_mapping = {
    'ID': 'entry',
    'School': 'school',
    'Category': 'category',
    'CastUI': 'castUI',
    'DispelType': 'dispel',
    'Mechanic': 'mechanic',
    'Attributes': 'attributes',
    'AttributesEx': 'attributesEx',
    'AttributesEx2': 'attributesEx2',
    'AttributesEx3': 'attributesEx3',
    'AttributesEx4': 'attributesEx4',
    'ShapeshiftMask': 'stances',
    'Shapeshiftexclude': 'stancesNot',
    'Targets': 'targets',
    'TargetCreatureType': 'targetCreatureType',
    'RequiresSpellFocus': 'requiresSpellFocus',
    'CasterAuraState': 'casterAuraState',
    'TargetAuraState': 'targetAuraState',
    'CastingTimeIndex': 'castingTimeIndex',
    'RecoveryTime': 'recoveryTime',
    'CategoryRecoveryTime': 'categoryRecoveryTime',
    'InterruptFlags': 'interruptFlags',
    'AuraInterruptFlags': 'auraInterruptFlags',
    'ChannelInterruptFlags': 'channelInterruptFlags',
    'ProcTypeMask': 'procFlags',
    'ProcChance': 'procChance',
    'ProcCharges': 'procCharges',
    'MaxLevel': 'maxLevel',
    'BaseLevel': 'baseLevel',
    'SpellLevel': 'spellLevel',
    'DurationIndex': 'durationIndex',
    'PowerType': 'powerType',
    'ManaCost': 'manaCost',
    'ManaCostPerLevel': 'manCostPerLevel',
    'ManaCostPerSecond': 'manaPerSecond',
    'ManaCostPerSecondPerLevel': 'manaPerSecondPerLevel',
    'RangeIndex': 'rangeIndex',
    'Speed': 'speed',
    'ModalNextSpell': 'modelNextSpell',
    'StackAmount': 'stackAmount',
    'Totem_1': 'totem1',
    'Totem_2': 'totem2',
    'Reagent_1': 'reagent1',
    'Reagent_2': 'reagent2',
    'Reagent_3': 'reagent3',
    'Reagent_4': 'reagent4',
    'Reagent_5': 'reagent5',
    'Reagent_6': 'reagent6',
    'Reagent_7': 'reagent7',
    'Reagent_8': 'reagent8',
    'ReagentCount_1': 'reagentCount1',
    'ReagentCount_2': 'reagentCount2',
    'ReagentCount_3': 'reagentCount3',
    'ReagentCount_4': 'reagentCount4',
    'ReagentCount_5': 'reagentCount5',
    'ReagentCount_6': 'reagentCount6',
    'ReagentCount_7': 'reagentCount7',
    'ReagentCount_8': 'reagentCount8',
    'EquippedItemClass': 'equippedItemClass',
    'EquippedItemSubclass': 'equippedItemSubClassMask',
    'EquippedItemInvType': 'equippedItemInventoryTypeMask',
    'Effect_1': 'effect1',
    'Effect_2': 'effect2',
    'Effect_3': 'effect3',
    'EffectDieSides_1': 'effectDieSides1',
    'EffectDieSides_2': 'effectDieSides2',
    'EffectDieSides_3': 'effectDieSides3',
    'EffectBaseDice_1': 'effectBaseDice1',
    'EffectBaseDice_2': 'effectBaseDice2',
    'EffectBaseDice_3': 'effectBaseDice3',
    'EffectDicePerLevel_1': 'effectDicePerLevel1',
    'EffectDicePerLevel_2': 'effectDicePerLevel2',
    'EffectDicePerLevel_3': 'effectDicePerLevel3',
    'EffectRealPointsPerLevel_1': 'effectRealPointsPerLevel1',
    'EffectRealPointsPerLevel_2': 'effectRealPointsPerLevel2',
    'EffectRealPointsPerLevel_3': 'effectRealPointsPerLevel3',
    'EffectBasePoints_1': 'effectBasePoints1',
    'EffectBasePoints_2': 'effectBasePoints2',
    'EffectBasePoints_3': 'effectBasePoints3',
    'EffectMechanic_1': 'effectMechanic1',
    'EffectMechanic_2': 'effectMechanic2',
    'EffectMechanic_3': 'effectMechanic3',
    'ImplicitTargetA_1': 'effectImplicitTargetA1',
    'ImplicitTargetA_2': 'effectImplicitTargetA2',
    'ImplicitTargetA_3': 'effectImplicitTargetA3',
    'ImplicitTargetB_1': 'effectImplicitTargetB1',
    'ImplicitTargetB_2': 'effectImplicitTargetB2',
    'ImplicitTargetB_3': 'effectImplicitTargetB3',
    'EffectRadiusIndex_1': 'effectRadiusIndex1',
    'EffectRadiusIndex_2': 'effectRadiusIndex2',
    'EffectRadiusIndex_3': 'effectRadiusIndex3',
    'EffectAura_1': 'effectApplyAuraName1',
    'EffectAura_2': 'effectApplyAuraName2',
    'EffectAura_3': 'effectApplyAuraName3',
    'EffectAmplitude_1': 'effectAmplitude1',
    'EffectAmplitude_2': 'effectAmplitude2',
    'EffectAmplitude_3': 'effectAmplitude3',
    'EffectMultipleValue_1': 'effectMultipleValue1',
    'EffectMultipleValue_2': 'effectMultipleValue2',
    'EffectMultipleValue_3': 'effectMultipleValue3',
    'EffectChainTarget_1': 'effectChainTarget1',
    'EffectChainTarget_2': 'effectChainTarget2',
    'EffectChainTarget_3': 'effectChainTarget3',
    'EffectItemType_1': 'effectItemType1',
    'EffectItemType_2': 'effectItemType2',
    'EffectItemType_3': 'effectItemType3',
    'EffectMiscValue_1': 'effectMiscValue1',
    'EffectMiscValue_2': 'effectMiscValue2',
    'EffectMiscValue_3': 'effectMiscValue3',
    'EffectTriggerSpell_1': 'effectTriggerSpell1',
    'EffectTriggerSpell_2': 'effectTriggerSpell2',
    'EffectTriggerSpell_3': 'effectTriggerSpell3',
    'EffectPointsPerCombo_1': 'effectPointsPerComboPoint1',
    'EffectPointsPerCombo_2': 'effectPointsPerComboPoint2',
    'EffectPointsPerCombo_3': 'effectPointsPerComboPoint3',
    'SpellVisualID_1': 'spellVisual1',
    'SpellVisualID_2': 'spellVisual2',
    'SpellIconID': 'spellIconId',
    'ActiveIconID': 'activeIconId',
    'SpellPriority': 'spellPriority',
    'Name_deDE': 'name',
    'Name_Mask': 'nameFlags',
    'NameSubtext_deDE': 'nameSubtext',
    'NameSubtext_Mask': 'nameSubtextFlags',
    'Description_deDE': 'description',
    'Description_Mask': 'descriptionFlags',
    'AuraDescription_deDE': 'auraDescription',
    'AuraDescription_Mask': 'auraDescriptionFlags',
    'ManaCostPct': 'manaCostPercentage',
    'StartRecoveryCategory': 'startRecoveryCategory',
    'StartRecoveryTime': 'startRecoveryTime',
    'MaxTargetLevel': 'maxTargetLevel',
    'SpellClassSet': 'spellFamilyName',
    'SpellClassMask_1': 'spellFamilyFlags',
    'MaxTargets': 'maxAffectedTargets',
    'DefenseType': 'dmgClass',
    'PreventionType': 'preventionType',
    'StanceBarOrder': 'stanceBarOrder',
    'DamageMultiplier_1': 'dmgMultiplier1',
    'DamageMultiplier_2': 'dmgMultiplier2',
    'DamageMultiplier_3': 'dmgMultiplier3',
    'MinFactionId': 'minFactionId',
    'MinReputation': 'minReputation',
    'RequiredAuraVision': 'requiredAuraVision'
}

# 读取 Excel
df_base = pd.read_excel(file_1171)  # 基准文件 (1171)
df_new = pd.read_excel(file_1180)   # 新文件 (1180)

# 保证两个表字段完全一致
if not (df_base.columns == df_new.columns).all():
    raise ValueError("两个表的字段不一致，请检查 Excel 文件！")

# 假设第一列是ID列，用于匹配行
id_column = df_base.columns[0]

# 创建基于ID的字典，方便查找
base_dict = df_base.set_index(id_column).to_dict('index')
new_dict = df_new.set_index(id_column).to_dict('index')

# 找出有改动的行和新增的行
changed_ids = []
new_ids = []
changed_fields_dict = {}  # 存储每个ID的差异字段信息

# 检查新文件中的每一行
for new_id, new_row in new_dict.items():
    if new_id in base_dict:
        # 存在于基准文件中，检查是否有改动
        base_row = base_dict[new_id]
        changed_fields = []

        for col in df_new.columns:
            if col in ignore_columns:
                continue

            base_val = base_row.get(col)
            new_val = new_row.get(col)

            # 比较值是否不同
            if pd.isna(base_val) and pd.isna(new_val):
                continue
            elif base_val != new_val:
                changed_fields.append(col)

        if changed_fields:
            changed_ids.append(new_id)
            changed_fields_dict[new_id] = changed_fields
    else:
        # 不存在于基准文件中，是新增的行
        new_ids.append(new_id)

# 创建一个手动构建的Excel文件，每个ID组合独立显示其差异字段
wb = Workbook()
ws = wb.active

# 定义颜色填充
blue_fill = PatternFill(start_color="FF87CEEB", end_color="FF87CEEB", fill_type="solid")
pink_fill = PatternFill(start_color="FFFFC0CB", end_color="FFFFC0CB", fill_type="solid")  # 浅粉色
light_purple_fill = PatternFill(start_color="FFE6E6FA", end_color="FFE6E6FA", fill_type="solid")  # 浅紫色
green_fill = PatternFill(start_color="FF00FF00", end_color="FF00FF00", fill_type="solid")
gray_fill = PatternFill(start_color="FFD3D3D3", end_color="FFD3D3D3", fill_type="solid")

current_row = 1

# 处理有改动的行 - 固定列结构
for changed_id in changed_ids:
    changed_fields = changed_fields_dict[changed_id]
    base_row = base_dict[changed_id]
    new_row = new_dict[changed_id]

    # 获取除了固定字段外的其他差异字段
    other_changed_fields = []
    for field in sorted(changed_fields):
        if field not in ['Name_deDE', 'Description_deDE']:
            other_changed_fields.append(field)

    # 第一行：字段名称行
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=changed_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='字段名称').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE
    if 'Name_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value='Name_deDE').fill = blue_fill
    else:
        ws.cell(row=current_row, column=current_col, value='Name_deDE')
    current_col += 1

    # 固定第4列：Description_deDE
    if 'Description_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value='Description_deDE').fill = blue_fill
    else:
        ws.cell(row=current_row, column=current_col, value='Description_deDE')
    current_col += 1

    # 其他差异字段
    for field in other_changed_fields:
        ws.cell(row=current_row, column=current_col, value=field).fill = blue_fill
        current_col += 1
    current_row += 1

    # 第二行：1171版本的值
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=changed_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='1171版本').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE的值
    name_value = base_row.get('Name_deDE', '')
    if 'Name_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value=name_value).fill = pink_fill
    else:
        ws.cell(row=current_row, column=current_col, value=name_value)
    current_col += 1

    # 固定第4列：Description_deDE的值
    desc_value = base_row.get('Description_deDE', '')
    if 'Description_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value=desc_value).fill = pink_fill
    else:
        ws.cell(row=current_row, column=current_col, value=desc_value)
    current_col += 1

    # 其他差异字段的值
    for field in other_changed_fields:
        ws.cell(row=current_row, column=current_col, value=base_row.get(field, '')).fill = pink_fill
        current_col += 1
    current_row += 1

    # 第三行：1180版本的值
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=changed_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='1180版本').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE的值
    name_value = new_row.get('Name_deDE', '')
    if 'Name_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value=name_value).fill = light_purple_fill
    else:
        ws.cell(row=current_row, column=current_col, value=name_value)
    current_col += 1

    # 固定第4列：Description_deDE的值
    desc_value = new_row.get('Description_deDE', '')
    if 'Description_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value=desc_value).fill = light_purple_fill
    else:
        ws.cell(row=current_row, column=current_col, value=desc_value)
    current_col += 1

    # 其他差异字段的值
    for field in other_changed_fields:
        ws.cell(row=current_row, column=current_col, value=new_row.get(field, '')).fill = light_purple_fill
        current_col += 1
    current_row += 1

# 处理新增的行 - 固定列结构
for new_id in new_ids:
    new_row = new_dict[new_id]

    # 获取除了固定字段外的其他非忽略字段
    other_new_fields = []
    for col in df_new.columns:
        if col not in ignore_columns and col != id_column and col not in ['Name_deDE', 'Description_deDE']:
            other_new_fields.append(col)

    sorted_other_fields = sorted(other_new_fields)

    # 新增行的字段名称行
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=new_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='新增-字段名').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE
    ws.cell(row=current_row, column=current_col, value='Name_deDE').fill = green_fill
    current_col += 1

    # 固定第4列：Description_deDE
    ws.cell(row=current_row, column=current_col, value='Description_deDE').fill = green_fill
    current_col += 1

    # 其他字段
    for field in sorted_other_fields:
        ws.cell(row=current_row, column=current_col, value=field).fill = green_fill
        current_col += 1
    current_row += 1

    # 新增行的值
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=new_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='新增-1180版本').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE的值
    ws.cell(row=current_row, column=current_col, value=new_row.get('Name_deDE', '')).fill = green_fill
    current_col += 1

    # 固定第4列：Description_deDE的值
    ws.cell(row=current_row, column=current_col, value=new_row.get('Description_deDE', '')).fill = green_fill
    current_col += 1

    # 其他字段的值
    for field in sorted_other_fields:
        ws.cell(row=current_row, column=current_col, value=new_row.get(field, '')).fill = green_fill
        current_col += 1
    current_row += 1

# 动态调整列宽 - 根据内容自动调整
for col in ws.columns:
    max_length = 0
    column = col[0].column_letter
    for cell in col:
        try:
            if len(str(cell.value)) > max_length:
                max_length = len(str(cell.value))
        except:
            pass
    adjusted_width = min(max_length + 2, 20)  # 最大宽度限制为20
    ws.column_dimensions[column].width = adjusted_width

# 保存文件
wb.save(output_file)

# 收集统计信息
all_changed_fields = set()
for fields in changed_fields_dict.values():
    all_changed_fields.update(fields)

print(f"比较完成！")
print(f"找到 {len(changed_ids)} 个ID有改动的数据（每个ID显示为3行组合）")
print(f"找到 {len(new_ids)} 行新增的数据（每个ID显示为2行组合）")
print(f"共有 {len(all_changed_fields)} 个字段存在差异")
print(f"差异字段列表: {', '.join(sorted(all_changed_fields))}")
print(f"结果已保存到 {output_file}")
print(f"\n显示格式说明：")
print(f"✅ 固定列结构：第1列=ID，第2列=行类型，第3列=Name_deDE，第4列=Description_deDE")
print(f"✅ 第5列及以后：动态显示其他差异字段")
print(f"✅ Name_deDE和Description_deDE始终显示，有差异时着色")
print(f"✅ 动态列宽，根据内容自动调整")
print(f"每个有差异的ID显示为3行组合：")
print(f"  第1行：字段名称（蓝色标记差异字段）")
print(f"  第2行：1171版本值（浅粉色标记差异字段）")
print(f"  第3行：1180版本值（浅紫色标记差异字段）")
print(f"新增的ID显示为2行组合：")
print(f"  第1行：字段名称（绿色标记）")
print(f"  第2行：1180版本值（绿色标记）")

if not changed_ids and not new_ids:
    print("没有发现改动或新增的数据")

# 生成MySQL UPDATE命令
def generate_mysql_updates():
    """生成MySQL UPDATE命令"""
    sql_commands = []

    # 处理有改动的行
    for changed_id in changed_ids:
        changed_fields = changed_fields_dict[changed_id]
        new_row = new_dict[changed_id]

        # 构建SET子句
        set_clauses = []
        for excel_field in changed_fields:
            if excel_field in ignore_columns:
                continue

            # 获取数据库字段名
            db_field = field_mapping.get(excel_field, excel_field)

            # 获取新值
            new_value = new_row.get(excel_field, '')

            # 处理NULL值和空字符串
            if pd.isna(new_value) or new_value == '':
                formatted_value = 'NULL'
            elif isinstance(new_value, str):
                # 转义单引号
                escaped_value = str(new_value).replace("'", "''")
                formatted_value = f"'{escaped_value}'"
            else:
                formatted_value = str(new_value)

            set_clauses.append(f"`{db_field}` = {formatted_value}")

        if set_clauses:
            # 构建完整的UPDATE语句
            update_sql = f"UPDATE `spell_template` SET {', '.join(set_clauses)} WHERE `entry` = {changed_id};"
            sql_commands.append(update_sql)

    return sql_commands

# 生成SQL命令
sql_commands = generate_mysql_updates()

# 保存SQL命令到文件
if sql_commands:
    with open(sql_output_file, 'w', encoding='utf-8') as f:
        f.write("-- MySQL UPDATE commands for spell_template changes\n")
        f.write(f"-- Generated from comparison between {file_1171} and {file_1180}\n")
        f.write(f"-- Total {len(sql_commands)} UPDATE statements\n\n")

        for i, sql in enumerate(sql_commands, 1):
            f.write(f"-- Update #{i}\n")
            f.write(sql + "\n\n")

    print(f"\n✅ 已生成 {len(sql_commands)} 条 MySQL UPDATE 命令")
    print(f"✅ SQL文件已保存到: {sql_output_file}")
else:
    print("\n❌ 没有需要生成UPDATE命令的数据变更")
